'use client'

import { resetPasswordSchema } from '../schema'
import useResetPassword from './useResetPassword'
import HeaderPage from '../components/HeaderPage'
import { Button } from '@/components/ui/button'
import { FormWrapper } from '@/components/core/FormWrapper'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'

const ResetPassword = () => {
  const { t, onSubmit, defaultValues } = useResetPassword()

  return (
    <>
      <HeaderPage title="reset_password" description="enter_new_password" />
      <FormWrapper defaultValues={defaultValues} onSubmit={onSubmit} schema={resetPasswordSchema}>
        <div className="flex flex-col gap-4">
          <FormPasswordInput
            className="min-w-[300px] sm:min-w-[380px]"
            name="password"
            label={t('label.new_password')}
            placeholder={t('label.new_password')}
          />
          <FormPasswordInput
            name="password_confirmation"
            label={t('label.new_password_confirmation')}
            placeholder={t('label.new_password_confirmation')}
          />
          <Button type="submit" className="w-full mt-11">
            {t('auth.save')}
          </Button>
        </div>
      </FormWrapper>
    </>
  )
}

export default ResetPassword
