import { actionService } from '@/services'
import { IResetPassword } from '@/types'
import { getCookie } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { toast } from 'sonner'

const defaultValues: IResetPassword = {
  email: '',
  code: '',
  password: '',
  password_confirmation: '',
}

const useResetPassword = () => {
  const t = useTranslations()
  const code = getCookie('code')
  const email = getCookie('em')

  const onSubmit = async (payload: IResetPassword) => {
    payload.email = email ?? ''
    payload.code = code ?? ''
    try {
      const result = await actionService(
        {
          path: 'auth/reset-password',
          method: 'POST',
        },
        null,
        payload
      )

      if (result.status) {
      }
      toast.success('')
      redirect('/auth/login')
    } catch (error) {
      console.error(':', error)
      toast.error('')
    }
  }

  return {
    t,
    onSubmit,
    defaultValues,
  }
}

export default useResetPassword
